# Tongsuo简单集成指南 - 开箱即用

## 🚀 一键集成

只需要在您的Trusty应用中添加一行依赖，即可使用Tongsuo的所有功能：

```makefile
# 在您的应用 rules.mk 中添加
MODULE_LIBRARY_DEPS += external/Tongsuo
```

就这么简单！无需任何额外配置。

## 📋 自动启用的功能

默认情况下，以下功能会自动启用：

- ✅ **SM2椭圆曲线算法** - 国密公钥算法
- ✅ **SM3哈希算法** - 国密摘要算法  
- ✅ **SM4分组密码** - 国密对称加密算法
- ✅ **TLCP协议** - 国密TLS协议
- ✅ **OpenSSL兼容API** - 无缝替换现有代码

## 💡 使用示例

### 基本应用结构

```
user/app/my_app/
├── rules.mk          # 构建配置
├── manifest.json     # 应用清单
└── main.c           # 主程序
```

### rules.mk 配置

```makefile
LOCAL_DIR := $(GET_LOCAL_DIR)

TRUSTY_APP_NAME := my_app

MODULE := $(LOCAL_DIR)
MANIFEST := $(LOCAL_DIR)/manifest.json

MODULE_SRCS += $(LOCAL_DIR)/main.c

# 一键集成Tongsuo - 就这一行！
MODULE_LIBRARY_DEPS += \
    trusty/user/base/lib/libstdc++-trusty \
    external/Tongsuo \
    trusty/user/base/lib/libc-rctee

include make/trusted_app.mk
```

### 代码示例

```c
// main.c
#include <stdio.h>
#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>
#include <openssl/ssl.h>

int main(void) {
    printf("Tongsuo integration test\n");
    
    // 初始化SSL库
    SSL_library_init();
    
    // 测试SM3哈希
    unsigned char hash[SM3_DIGEST_LENGTH];
    SM3((unsigned char*)"Hello", 5, hash);
    printf("SM3 hash computed successfully\n");
    
    // 测试SM2密钥生成
    EC_KEY *key = EC_KEY_new_by_curve_name(NID_sm2);
    if (key && EC_KEY_generate_key(key)) {
        printf("SM2 key generated successfully\n");
        EC_KEY_free(key);
    }
    
    // 测试SM4加密
    SM4_KEY sm4_key;
    unsigned char sm4_user_key[16] = {0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
                                      0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10};
    SM4_set_key(sm4_user_key, &sm4_key);
    printf("SM4 key set successfully\n");
    
    printf("All tests passed!\n");
    return 0;
}
```

### manifest.json

```json
{
    "app_name": "my_app",
    "uuid": "12345678-1234-5678-9abc-123456789abc",
    "min_heap": 65536,
    "min_stack": 8192,
    "mgmt_flags": {
        "restart_on_exit": false
    }
}
```

## 🔧 构建和运行

```bash
# 构建应用
make COMPILER_PATH=/path/to/compiler my_app

# 构建整个项目
make COMPILER_PATH=/path/to/compiler testproject
```

## ⚙️ 可选配置

如果需要启用高级功能，可以在构建时指定：

```bash
# 启用零知识证明
make TONGSUO_ENABLE_ZKP=true my_app

# 启用同态加密
make TONGSUO_ENABLE_PAILLIER=true my_app

# 启用所有功能
make TONGSUO_ENABLE_ZKP=true TONGSUO_ENABLE_PAILLIER=true TONGSUO_ENABLE_EC_ELGAMAL=true my_app
```

或者在项目的全局配置中设置：

```makefile
# 在项目根目录的 Makefile 或配置文件中
TONGSUO_ENABLE_ZKP := true
TONGSUO_ENABLE_PAILLIER := true
```

## 🔍 与BoringSSL对比

| 操作 | BoringSSL | Tongsuo |
|------|-----------|---------|
| 集成方式 | `MODULE_LIBRARY_DEPS += external/boringssl` | `MODULE_LIBRARY_DEPS += external/Tongsuo` |
| 国际算法 | ✅ | ✅ |
| 国密算法 | ❌ | ✅ |
| API兼容 | OpenSSL 1.1.1 | OpenSSL 1.1.1 |
| 配置复杂度 | 简单 | 简单 |

## 🛠️ 故障排除

### 常见问题

1. **编译错误：找不到头文件**
   ```
   解决：确保 MODULE_LIBRARY_DEPS += external/Tongsuo 在正确位置
   ```

2. **链接错误：符号未定义**
   ```
   解决：检查是否包含了必要的库依赖
   ```

3. **运行时错误：内存不足**
   ```
   解决：在manifest.json中增加min_heap和min_stack大小
   ```

### 调试技巧

```bash
# 查看详细构建信息
make V=1 my_app

# 检查Tongsuo配置
make TONGSUO_DEBUG=true my_app
```

## 📚 API参考

Tongsuo提供完整的OpenSSL 1.1.1兼容API，额外支持：

### 国密算法API

```c
// SM2椭圆曲线
EC_KEY *EC_KEY_new_by_curve_name(int nid);  // nid = NID_sm2

// SM3哈希
int SM3_Init(SM3_CTX *c);
int SM3_Update(SM3_CTX *c, const void *data, size_t len);
int SM3_Final(unsigned char *md, SM3_CTX *c);

// SM4分组密码
int SM4_set_key(const unsigned char *userKey, SM4_KEY *key);
void SM4_encrypt(const unsigned char *in, unsigned char *out, const SM4_KEY *key);
void SM4_decrypt(const unsigned char *in, unsigned char *out, const SM4_KEY *key);
```

### TLCP协议API

```c
// 创建TLCP上下文
SSL_CTX *SSL_CTX_new(const SSL_METHOD *method);  // method = NTLS_client_method()

// 设置国密密码套件
int SSL_CTX_set_cipher_list(SSL_CTX *ctx, const char *str);  // str = "ECC-SM2-SM4-CBC-SM3"
```

## 🎯 总结

通过这个简化的集成方案，您可以：

1. **零配置集成** - 只需一行依赖声明
2. **自动功能检测** - 智能启用可用功能
3. **开箱即用** - 无需手动配置源文件
4. **完全兼容** - 与现有BoringSSL使用方式一致
5. **国密支持** - 自动启用SM2/SM3/SM4算法

这就是您需要的全部！享受Tongsuo带来的强大密码学功能吧！
