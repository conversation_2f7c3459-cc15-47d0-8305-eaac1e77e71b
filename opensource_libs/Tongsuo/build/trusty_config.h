/* Copyright (C) 2024 The Trusty Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo configuration for Trusty environment
 * This file provides Trusty-specific configuration overrides for Tongsuo
 */

#ifndef TONGSUO_TRUSTY_CONFIG_H
#define TONGSUO_TRUSTY_CONFIG_H

/* Trusty environment detection */
#ifdef __TRUSTY__

/* Basic OpenSSL configuration */
#define OPENSSL_API_COMPAT 0x10101000L
#define OPENSSL_MIN_API 0x10101000L

/* Disable features not available in Trusty */
#define OPENSSL_NO_STDIO
#define OPENSSL_NO_SOCK
#define OPENSSL_NO_DGRAM
#define OPENSSL_NO_UI_CONSOLE
#define OPENSSL_NO_DEPRECATED
#define OPENSSL_NO_DYNAMIC_ENGINE
#define OPENSSL_NO_AUTOLOAD_CONFIG
#define OPENSSL_NO_AUTOERRINIT
#define OPENSSL_NO_APPS
#define OPENSSL_NO_TESTS
#define OPENSSL_NO_FUZZ_LIBFUZZER
#define OPENSSL_NO_FUZZ_AFL
#define OPENSSL_NO_UNIT_TEST
#define OPENSSL_NO_SECURE_MEMORY
#define OPENSSL_NO_HEARTBEATS
#define OPENSSL_NO_COMP

/* Enable Chinese National Cryptographic Algorithms */
#define OPENSSL_ENABLE_SM2
#define OPENSSL_ENABLE_SM3
#define OPENSSL_ENABLE_SM4
#define OPENSSL_ENABLE_NTLS

/* Threading configuration */
#define OPENSSL_THREADS
#define _REENTRANT

/* Memory optimization for embedded environment */
#define OPENSSL_SMALL

/* Architecture-specific configurations */
#ifdef __aarch64__
#define OPENSSL_AARCH64
#define OPENSSL_STATIC_ARMCAP_NEON
#define OPENSSL_STATIC_ARMCAP_AES
#define OPENSSL_STATIC_ARMCAP_SHA1
#define OPENSSL_STATIC_ARMCAP_SHA256
#endif

#ifdef __arm__
#define OPENSSL_ARM
#define OPENSSL_STATIC_ARMCAP
#endif

#ifdef __x86_64__
#define OPENSSL_X86_64
#endif

/* Trusty-specific function replacements */
#ifdef OPENSSL_NO_STDIO
/* Disable file I/O operations */
#define BIO_new_file(filename, mode) NULL
#define BIO_new_fp(stream, close_flag) NULL
#endif

/* Random number generation configuration */
#define OPENSSL_RAND_SEED_NONE

/* Error handling configuration */
#define OPENSSL_NO_ERR_TRACE

/* Crypto configuration */
#define OPENSSL_NO_CAMELLIA
#define OPENSSL_NO_CAST
#define OPENSSL_NO_IDEA
#define OPENSSL_NO_MDC2
#define OPENSSL_NO_RC2
#define OPENSSL_NO_RC5
#define OPENSSL_NO_SEED
#define OPENSSL_NO_WHIRLPOOL

/* Protocol configuration */
#define OPENSSL_NO_SSL3_METHOD
#define OPENSSL_NO_WEAK_SSL_CIPHERS

/* Optional features (can be enabled via build configuration) */
#ifndef TONGSUO_ENABLE_ZKP
#define OPENSSL_NO_ZKP
#endif

#ifndef TONGSUO_ENABLE_PAILLIER
#define OPENSSL_NO_PAILLIER
#endif

#ifndef TONGSUO_ENABLE_EC_ELGAMAL
#define OPENSSL_NO_EC_ELGAMAL
#endif

/* Trusty memory allocation wrappers */
#ifdef __TRUSTY__
#include <stdlib.h>
#include <string.h>

/* Use Trusty's memory allocation functions */
#define OPENSSL_malloc(size) malloc(size)
#define OPENSSL_realloc(ptr, size) realloc(ptr, size)
#define OPENSSL_free(ptr) free(ptr)
#define OPENSSL_clear_free(ptr, size) do { \
    if (ptr) { \
        memset(ptr, 0, size); \
        free(ptr); \
    } \
} while(0)

/* Secure memory operations */
#define OPENSSL_cleanse(ptr, len) memset(ptr, 0, len)

/* Time functions */
#include <time.h>
#define time(t) time(t)

/* Threading primitives */
#include <pthread.h>
typedef pthread_mutex_t CRYPTO_MUTEX;
#define CRYPTO_MUTEX_INIT PTHREAD_MUTEX_INITIALIZER

static inline int CRYPTO_THREAD_init_local(CRYPTO_THREAD_LOCAL *key, void (*cleanup)(void *)) {
    return pthread_key_create(key, cleanup) == 0;
}

static inline void *CRYPTO_THREAD_get_local(CRYPTO_THREAD_LOCAL *key) {
    return pthread_getspecific(*key);
}

static inline int CRYPTO_THREAD_set_local(CRYPTO_THREAD_LOCAL *key, void *val) {
    return pthread_setspecific(*key, val) == 0;
}

static inline int CRYPTO_THREAD_cleanup_local(CRYPTO_THREAD_LOCAL *key) {
    return pthread_key_delete(*key) == 0;
}

static inline CRYPTO_THREAD_ID CRYPTO_THREAD_get_current_id(void) {
    return pthread_self();
}

static inline int CRYPTO_THREAD_compare_id(CRYPTO_THREAD_ID a, CRYPTO_THREAD_ID b) {
    return pthread_equal(a, b);
}

static inline int CRYPTO_THREAD_lock_init(CRYPTO_MUTEX *mutex) {
    return pthread_mutex_init(mutex, NULL) == 0;
}

static inline int CRYPTO_THREAD_lock_free(CRYPTO_MUTEX *mutex) {
    return pthread_mutex_destroy(mutex) == 0;
}

static inline int CRYPTO_THREAD_write_lock(CRYPTO_MUTEX *mutex) {
    return pthread_mutex_lock(mutex) == 0;
}

static inline int CRYPTO_THREAD_read_lock(CRYPTO_MUTEX *mutex) {
    return pthread_mutex_lock(mutex) == 0;
}

static inline int CRYPTO_THREAD_unlock(CRYPTO_MUTEX *mutex) {
    return pthread_mutex_unlock(mutex) == 0;
}

#endif /* __TRUSTY__ */

/* Logging configuration */
#ifdef __TRUSTY__
#include <stdio.h>
#define OPENSSL_LOG_INFO(fmt, ...) printf("[TONGSUO INFO] " fmt "\n", ##__VA_ARGS__)
#define OPENSSL_LOG_ERROR(fmt, ...) printf("[TONGSUO ERROR] " fmt "\n", ##__VA_ARGS__)
#define OPENSSL_LOG_DEBUG(fmt, ...) printf("[TONGSUO DEBUG] " fmt "\n", ##__VA_ARGS__)
#else
#define OPENSSL_LOG_INFO(fmt, ...)
#define OPENSSL_LOG_ERROR(fmt, ...)
#define OPENSSL_LOG_DEBUG(fmt, ...)
#endif

/* Version information */
#define TONGSUO_TRUSTY_VERSION "1.0.0"
#define TONGSUO_TRUSTY_BUILD_DATE __DATE__

#endif /* __TRUSTY__ */

#endif /* TONGSUO_TRUSTY_CONFIG_H */
