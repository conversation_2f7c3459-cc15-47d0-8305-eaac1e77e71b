#!/usr/bin/env python3
# Copyright (C) 2024 The Trusty Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Generate Makefile-compatible source file lists from Tongsuo build.info files.

This script parses Tongsuo's build.info files and generates tongsuo-sources.mk
for use with the Trusty build system, similar to how BoringSSL generates its
source lists.
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

class BuildInfoParser:
    """Parser for Tongsuo build.info files."""
    
    def __init__(self, tongsuo_root: Path):
        self.tongsuo_root = tongsuo_root
        self.crypto_sources = []
        self.ssl_sources = []
        self.zkp_sources = []
        self.paillier_sources = []
        self.ec_elgamal_sources = []
        self.asm_sources = {
            'arm64': [],
            'arm': [],
            'x86_64': []
        }
        
    def parse_build_info(self, build_info_path: Path) -> Dict[str, List[str]]:
        """Parse a single build.info file."""
        sources = {}
        current_target = None
        
        with open(build_info_path, 'r') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                    
                # Parse SOURCE[target]=files
                source_match = re.match(r'SOURCE\[([^\]]+)\]\s*=\s*(.+)', line)
                if source_match:
                    target = source_match.group(1)
                    files = source_match.group(2).split()
                    if target not in sources:
                        sources[target] = []
                    sources[target].extend(files)
                    continue
                
                # Handle continuation lines
                if line.endswith('\\'):
                    # This is a continuation line, would need more complex parsing
                    pass
                    
        return sources
    
    def is_assembly_file(self, filename: str) -> bool:
        """Check if a file is an assembly source file."""
        return filename.endswith(('.S', '.s', '.asm'))
    
    def get_architecture_from_path(self, filepath: str) -> str:
        """Determine target architecture from file path."""
        if 'arm64' in filepath or 'aarch64' in filepath:
            return 'arm64'
        elif 'arm' in filepath and 'arm64' not in filepath:
            return 'arm'
        elif 'x86_64' in filepath or 'x64' in filepath:
            return 'x86_64'
        elif 'x86' in filepath:
            return 'x86'
        return 'generic'
    
    def categorize_source(self, filepath: str, relative_path: str):
        """Categorize a source file into appropriate lists."""
        # Skip test files, apps, and other non-library code
        if any(skip in relative_path for skip in ['test/', 'apps/', 'fuzz/', 'util/']):
            return
            
        # Skip platform-specific files not suitable for Trusty
        if any(skip in relative_path for skip in ['o_dir.c', 'o_fopen.c', 'ui_openssl.c']):
            return
            
        if self.is_assembly_file(filepath):
            arch = self.get_architecture_from_path(relative_path)
            if arch in self.asm_sources:
                self.asm_sources[arch].append(relative_path)
        elif relative_path.startswith('crypto/zkp/'):
            self.zkp_sources.append(relative_path)
        elif relative_path.startswith('crypto/paillier/'):
            self.paillier_sources.append(relative_path)
        elif 'ec_elgamal' in relative_path:
            self.ec_elgamal_sources.append(relative_path)
        elif relative_path.startswith('crypto/'):
            self.crypto_sources.append(relative_path)
        elif relative_path.startswith('ssl/'):
            self.ssl_sources.append(relative_path)
    
    def scan_directory(self, directory: Path, base_path: Path = None):
        """Recursively scan directory for source files."""
        if base_path is None:
            base_path = self.tongsuo_root
            
        for item in directory.iterdir():
            if item.is_file() and item.suffix in ['.c', '.S', '.s']:
                relative_path = str(item.relative_to(base_path))
                self.categorize_source(str(item), relative_path)
            elif item.is_dir() and not item.name.startswith('.'):
                # Skip certain directories
                if item.name not in ['test', 'apps', 'fuzz', 'util', 'doc', 'demos']:
                    self.scan_directory(item, base_path)
    
    def generate_makefile_sources(self, output_path: Path):
        """Generate the tongsuo-sources.mk file."""
        with open(output_path, 'w') as f:
            f.write("""# Copyright (C) 2024 The Trusty Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# This file is generated from Tongsuo build.info files
# Do not edit manually - use generate_sources.py to regenerate

""")
            
            # Write crypto sources
            f.write("# Core crypto library sources\n")
            f.write("TONGSUO_CRYPTO_SRCS := \\\n")
            for i, src in enumerate(sorted(self.crypto_sources)):
                if i == len(self.crypto_sources) - 1:
                    f.write(f"    {src}\n\n")
                else:
                    f.write(f"    {src} \\\n")
            
            # Write SSL sources
            f.write("# SSL/TLS library sources\n")
            f.write("TONGSUO_SSL_SRCS := \\\n")
            for i, src in enumerate(sorted(self.ssl_sources)):
                if i == len(self.ssl_sources) - 1:
                    f.write(f"    {src}\n\n")
                else:
                    f.write(f"    {src} \\\n")
            
            # Write optional feature sources
            if self.zkp_sources:
                f.write("# Optional: Zero Knowledge Proof sources (if enabled)\n")
                f.write("TONGSUO_ZKP_SRCS := \\\n")
                for i, src in enumerate(sorted(self.zkp_sources)):
                    if i == len(self.zkp_sources) - 1:
                        f.write(f"    {src}\n\n")
                    else:
                        f.write(f"    {src} \\\n")
            
            if self.paillier_sources:
                f.write("# Optional: Paillier homomorphic encryption sources (if enabled)\n")
                f.write("TONGSUO_PAILLIER_SRCS := \\\n")
                for i, src in enumerate(sorted(self.paillier_sources)):
                    if i == len(self.paillier_sources) - 1:
                        f.write(f"    {src}\n\n")
                    else:
                        f.write(f"    {src} \\\n")
            
            if self.ec_elgamal_sources:
                f.write("# Optional: EC-ElGamal sources (if enabled)\n")
                f.write("TONGSUO_EC_ELGAMAL_SRCS := \\\n")
                for i, src in enumerate(sorted(self.ec_elgamal_sources)):
                    if i == len(self.ec_elgamal_sources) - 1:
                        f.write(f"    {src}\n\n")
                    else:
                        f.write(f"    {src} \\\n")
            
            # Write architecture-specific assembly sources
            f.write("# Architecture-specific assembly sources\n")
            for arch, sources in self.asm_sources.items():
                if sources:
                    f.write(f"TONGSUO_ASM_SRCS_{arch.upper()} := \\\n")
                    for i, src in enumerate(sorted(sources)):
                        if i == len(sources) - 1:
                            f.write(f"    {src}\n\n")
                        else:
                            f.write(f"    {src} \\\n")

def main():
    """Main entry point."""
    if len(sys.argv) != 2:
        print("Usage: generate_sources.py <tongsuo_root>")
        sys.exit(1)
    
    tongsuo_root = Path(sys.argv[1]).resolve()
    if not tongsuo_root.exists():
        print(f"Error: Tongsuo root directory {tongsuo_root} does not exist")
        sys.exit(1)
    
    parser = BuildInfoParser(tongsuo_root)
    
    # Scan crypto and ssl directories
    crypto_dir = tongsuo_root / 'crypto'
    ssl_dir = tongsuo_root / 'ssl'
    
    if crypto_dir.exists():
        parser.scan_directory(crypto_dir)
    
    if ssl_dir.exists():
        parser.scan_directory(ssl_dir)
    
    # Generate the output file
    output_path = tongsuo_root / 'tongsuo-sources.mk'
    parser.generate_makefile_sources(output_path)
    
    print(f"Generated {output_path}")
    print(f"Found {len(parser.crypto_sources)} crypto sources")
    print(f"Found {len(parser.ssl_sources)} SSL sources")
    print(f"Found {len(parser.zkp_sources)} ZKP sources")
    print(f"Found {len(parser.paillier_sources)} Paillier sources")
    print(f"Found {len(parser.ec_elgamal_sources)} EC-ElGamal sources")
    for arch, sources in parser.asm_sources.items():
        if sources:
            print(f"Found {len(sources)} {arch} assembly sources")

if __name__ == '__main__':
    main()
