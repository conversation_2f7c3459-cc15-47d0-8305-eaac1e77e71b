
# configs for tools
CLANG_BINDIR ?=
CLANG_HOST_LIBDIR ?=
CLANG_TOOLS_BINDIR ?=
LINUX_CLANG_BINDIR ?=
ARCH_arm_TOOLCHAIN_PREFIX ?=
ARCH_arm64_TOOLCHAIN_PREFIX ?=
BUILDTOOLS_BINDIR ?=
BUILDTOOLS_COMMON ?=
PY3 ?=
PYTHONPATH ?=
OUT_DIR ?= out

# ensure out dir is exists
ifeq ($(wildcard $(OUT_DIR)),)
build-root:
	@echo "Creating $(OUT_DIR) folder"
	@mkdir -p $(OUT_DIR)
else
build-root:
	@echo "$(OUT_DIR) folder already exists"
endif
.PHONY: build-root


#args for little kernel
LKMAKEROOT ?= .
# paths relative to LKMAKEROOT where additional modules should be searched
LKINC ?=  build/tools \
		  kernel/rctee \
		  user/base \
		  opensource_libs/headers \
		  opensource_libs \
		  kernel/rctee/platform/nxp/imx8 \
		  kernel/hardware/nxp \

# the path relative to LKMAKEROOT where the main lk repository lives
LKROOT ?= kernel/lk

# set the directory relative to LKMAKEROOT where output will go
BUILDROOT ?= $(OUT_DIR)

# set the default project if no args are passed
DEFAULT_PROJECT ?= testproject


# check complier path
ifeq ($(COMPILER_PATH),)
$(error error: complier path must be given!!!)
else
CLANG_BINDIR=$(COMPILER_PATH)/clang/host/linux-x86/clang-r475365b/bin
CLANG_HOST_LIBDIR=$(CLANG_BINDIR)/../lib
CLANG_TOOLS_BINDIR=$(COMPILER_PATH)/clang-tools/linux-x86/bin
LINUX_CLANG_BINDIR=$(COMPILER_PATH)/clang/host/linux-x86/clang-r475365b/bin
ARCH_arm_TOOLCHAIN_PREFIX=$(CLANG_BINDIR)/llvm-
ARCH_arm64_TOOLCHAIN_PREFIX=$(CLANG_BINDIR)/llvm-
BUILDTOOLS_BINDIR=$(COMPILER_PATH)/build-tools/linux-x86/bin
BUILDTOOLS_COMMON=$(COMPILER_PATH)/build-tools/common
PY3=$(BUILDTOOLS_BINDIR)/py3-cmd
PYTHONPATH=$(COMPILER_PATH)/libprotobuf/python

#export build vars
export CLANG_BINDIR
export CLANG_HOST_LIBDIR
export CLANG_TOOLS_BINDIR
export LINUX_CLANG_BINDIR
export ARCH_arm_TOOLCHAIN_PREFIX
export ARCH_arm64_TOOLCHAIN_PREFIX
export BUILDTOOLS_BINDIR
export BUILDTOOLS_COMMON
export PY3
export PYTHONPATH
endif

# check if LKROOT is already a part of LKINC list and add it only if it is not
ifeq ($(filter $(LKROOT),$(LKINC)), )
LKINC := $(LKROOT) $(LKINC)
endif

# add the external path to LKINC
ifneq ($(LKROOT),.)
LKINC += $(LKROOT)/external
else
LKINC += external
endif

export LKMAKEROOT
export LKROOT
export LKINC
export BUILDROOT
export DEFAULT_PROJECT
export TOOLCHAIN_PREFIX

# veneer makefile that calls into the engine with lk as the build root
# if we're the top level invocation, call ourselves with additional args
_top:
	@$(MAKE) -C $(LKMAKEROOT) -rR -f $(LKROOT)/engine.mk $(addprefix -I,$(LKINC)) $(MAKECMDGOALS)

# If any arguments were provided, create a recipe for them that depends
# on the _top rule (thus calling it), but otherwise do nothing.
# "@:" (vs empty rule ";") prevents extra "'foo' is up to date." messages from
# being emitted.
$(MAKECMDGOALS): _top build-root
	@:

.PHONY: _top
