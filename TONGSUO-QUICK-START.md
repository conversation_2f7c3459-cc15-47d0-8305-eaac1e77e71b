# Tongsuo快速开始指南

## 🎯 一键集成 - 3分钟搞定

### 第1步：创建符号链接（一次性操作）

```bash
# 在trusty-tee根目录下执行
mkdir -p external
ln -sf ../opensource_libs/Tongsuo external/Tongsuo
```

### 第2步：在应用中使用

在您的应用 `rules.mk` 中添加一行：

```makefile
MODULE_LIBRARY_DEPS += external/Tongsuo
```

### 第3步：编写代码

```c
#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>

int main(void) {
    // 直接使用国密算法
    EC_KEY *key = EC_KEY_new_by_curve_name(NID_sm2);
    // ... 其他代码
    return 0;
}
```

### 第4步：构建

```bash
make COMPILER_PATH=/path/to/compiler your_app
```

## ✅ 验证集成

我们提供了一个完整的测试应用来验证集成：

```bash
# 构建测试应用
make COMPILER_PATH=/path/to/compiler tongsuo_test

# 运行测试
./out/build-testproject/lk.bin
```

测试应用会验证：
- ✅ SM2椭圆曲线算法
- ✅ SM3哈希算法
- ✅ SM4对称加密算法
- ✅ 基本OpenSSL功能

## 🔧 完整示例

### 应用目录结构

```
user/app/my_app/
├── rules.mk          # 构建配置
├── manifest.json     # 应用清单
└── main.c           # 主程序
```

### rules.mk

```makefile
LOCAL_DIR := $(GET_LOCAL_DIR)
TRUSTY_APP_NAME := my_app
MODULE := $(LOCAL_DIR)

MODULE_SRCS += $(LOCAL_DIR)/main.c

# 一键集成Tongsuo
MODULE_LIBRARY_DEPS += \
    trusty/user/base/lib/libstdc++-trusty \
    external/Tongsuo \
    trusty/user/base/lib/libc-rctee

include make/trusted_app.mk
```

### manifest.json

```json
{
    "app_name": "my_app",
    "uuid": "12345678-1234-5678-9abc-123456789abc",
    "min_heap": 65536,
    "min_stack": 8192,
    "mgmt_flags": {
        "restart_on_exit": false
    }
}
```

### main.c

```c
#include <stdio.h>
#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>
#include <openssl/ssl.h>

int main(void) {
    printf("Tongsuo integration test\n");
    
    // 初始化SSL库
    SSL_library_init();
    
    // 测试SM3哈希
    unsigned char hash[SM3_DIGEST_LENGTH];
    SM3((unsigned char*)"Hello", 5, hash);
    printf("SM3 hash computed successfully\n");
    
    // 测试SM2密钥生成
    EC_KEY *key = EC_KEY_new_by_curve_name(NID_sm2);
    if (key && EC_KEY_generate_key(key)) {
        printf("SM2 key generated successfully\n");
        EC_KEY_free(key);
    }
    
    // 测试SM4加密
    SM4_KEY sm4_key;
    unsigned char sm4_user_key[16] = {0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
                                      0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10};
    SM4_set_key(sm4_user_key, &sm4_key);
    printf("SM4 key set successfully\n");
    
    printf("All tests passed!\n");
    return 0;
}
```

## 🚀 高级功能

### 启用可选特性

```bash
# 启用零知识证明
make TONGSUO_ENABLE_ZKP=true my_app

# 启用同态加密
make TONGSUO_ENABLE_PAILLIER=true my_app

# 启用所有高级特性
make TONGSUO_ENABLE_ZKP=true TONGSUO_ENABLE_PAILLIER=true TONGSUO_ENABLE_EC_ELGAMAL=true my_app
```

### 构建信息

构建时会显示启用的功能：

```
Tongsuo: Building with SM2=true SM3=true SM4=true NTLS=true
Tongsuo: Advanced features - ZKP=false Paillier=false EC-ElGamal=false
```

## 🛠️ 故障排除

### 常见问题

1. **找不到external/Tongsuo/rules.mk**
   ```bash
   # 解决：创建符号链接
   ln -sf ../opensource_libs/Tongsuo external/Tongsuo
   ```

2. **编译错误：找不到头文件**
   ```makefile
   # 确保正确添加依赖
   MODULE_LIBRARY_DEPS += external/Tongsuo
   ```

3. **链接错误：符号未定义**
   ```makefile
   # 确保包含必要的库
   MODULE_LIBRARY_DEPS += \
       trusty/user/base/lib/libstdc++-trusty \
       external/Tongsuo \
       trusty/user/base/lib/libc-rctee
   ```

### 调试技巧

```bash
# 详细构建信息
make V=1 my_app

# 检查Tongsuo配置
make TONGSUO_DEBUG=true my_app
```

## 📚 API参考

### 国密算法

```c
// SM2椭圆曲线
EC_KEY *EC_KEY_new_by_curve_name(int nid);  // nid = NID_sm2

// SM3哈希
int SM3_Init(SM3_CTX *c);
int SM3_Update(SM3_CTX *c, const void *data, size_t len);
int SM3_Final(unsigned char *md, SM3_CTX *c);

// SM4分组密码
int SM4_set_key(const unsigned char *userKey, SM4_KEY *key);
void SM4_encrypt(const unsigned char *in, unsigned char *out, const SM4_KEY *key);
void SM4_decrypt(const unsigned char *in, unsigned char *out, const SM4_KEY *key);
```

### TLCP协议

```c
// 创建TLCP上下文
SSL_CTX *SSL_CTX_new(const SSL_METHOD *method);  // method = NTLS_client_method()

// 设置国密密码套件
int SSL_CTX_set_cipher_list(SSL_CTX *ctx, const char *str);  // str = "ECC-SM2-SM4-CBC-SM3"
```

## 🎉 完成！

现在您已经成功集成了Tongsuo，可以在Trusty应用中使用强大的国密算法和前沿密码学功能了！

如有问题，请参考完整文档：
- `README-Tongsuo-Integration.md` - 完整集成指南
- `tongsuo-simple-integration.md` - 详细使用示例
- `tongsuo-integration-plan.md` - 技术实现方案
