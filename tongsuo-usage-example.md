# Tongsuo使用示例

## 1. 基本集成使用

### 1.1 在Trusty应用中使用Tongsuo

创建一个简单的Trusty应用来测试Tongsuo集成：

```c
// user/app/tongsuo_test/main.c
#include <stdio.h>
#include <string.h>
#include <openssl/evp.h>
#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>
#include <openssl/ssl.h>

int main(void) {
    printf("Tongsuo integration test starting...\n");
    
    // 初始化OpenSSL
    SSL_library_init();
    SSL_load_error_strings();
    
    // 测试SM3哈希算法
    test_sm3_hash();
    
    // 测试SM4对称加密
    test_sm4_cipher();
    
    // 测试SM2椭圆曲线算法
    test_sm2_keypair();
    
    printf("Tongsuo integration test completed successfully!\n");
    return 0;
}

void test_sm3_hash(void) {
    const char *message = "Hello, <PERSON><PERSON><PERSON>!";
    unsigned char hash[SM3_DIGEST_LENGTH];
    
    SM3_CTX ctx;
    SM3_Init(&ctx);
    SM3_Update(&ctx, message, strlen(message));
    SM3_Final(hash, &ctx);
    
    printf("SM3 hash test passed\n");
}

void test_sm4_cipher(void) {
    const unsigned char key[16] = {0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
                                   0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10};
    const unsigned char plaintext[16] = "Hello SM4!";
    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    
    SM4_KEY sm4_key;
    SM4_set_key(key, &sm4_key);
    
    SM4_encrypt(plaintext, ciphertext, &sm4_key);
    SM4_decrypt(ciphertext, decrypted, &sm4_key);
    
    if (memcmp(plaintext, decrypted, 16) == 0) {
        printf("SM4 cipher test passed\n");
    }
}

void test_sm2_keypair(void) {
    EC_KEY *key = EC_KEY_new_by_curve_name(NID_sm2);
    if (!key) {
        printf("Failed to create SM2 key\n");
        return;
    }
    
    if (EC_KEY_generate_key(key) == 1) {
        printf("SM2 keypair generation test passed\n");
    }
    
    EC_KEY_free(key);
}
```

### 1.2 应用构建配置

```makefile
# user/app/tongsuo_test/rules.mk
LOCAL_DIR := $(GET_LOCAL_DIR)

TRUSTY_APP_NAME := tongsuo_test

MODULE := $(LOCAL_DIR)

MANIFEST := $(LOCAL_DIR)/manifest.json

MODULE_SRCS += \
    $(LOCAL_DIR)/main.c

MODULE_LIBRARY_DEPS += \
    trusty/user/base/lib/libstdc++-trusty \
    external/Tongsuo \
    trusty/user/base/lib/libc-rctee

include make/trusted_app.mk
```

### 1.3 应用清单文件

```json
{
    "app_name": "tongsuo_test",
    "uuid": "12345678-1234-5678-9abc-123456789abc",
    "min_heap": 65536,
    "min_stack": 8192,
    "mgmt_flags": {
        "restart_on_exit": false
    }
}
```

## 2. 国密算法使用示例

### 2.1 SM2数字签名

```c
#include <openssl/sm2.h>
#include <openssl/evp.h>

int sm2_sign_verify_example(void) {
    EC_KEY *key = NULL;
    EVP_PKEY *pkey = NULL;
    EVP_PKEY_CTX *pctx = NULL;
    EVP_MD_CTX *md_ctx = NULL;
    const char *message = "Message to be signed";
    unsigned char *signature = NULL;
    size_t sig_len = 0;
    int ret = 0;
    
    // 创建SM2密钥对
    key = EC_KEY_new_by_curve_name(NID_sm2);
    if (!key || !EC_KEY_generate_key(key)) {
        goto cleanup;
    }
    
    pkey = EVP_PKEY_new();
    if (!pkey || !EVP_PKEY_set1_EC_KEY(pkey, key)) {
        goto cleanup;
    }
    
    // 设置SM2用户ID
    if (!EVP_PKEY_set_alias_type(pkey, EVP_PKEY_SM2)) {
        goto cleanup;
    }
    
    // 创建签名上下文
    md_ctx = EVP_MD_CTX_new();
    if (!md_ctx) {
        goto cleanup;
    }
    
    // 初始化签名操作
    if (EVP_DigestSignInit(md_ctx, &pctx, EVP_sm3(), NULL, pkey) <= 0) {
        goto cleanup;
    }
    
    // 设置SM2用户ID
    if (EVP_PKEY_CTX_set1_id(pctx, "1234567812345678", 16) <= 0) {
        goto cleanup;
    }
    
    // 计算签名长度
    if (EVP_DigestSign(md_ctx, NULL, &sig_len, 
                       (const unsigned char*)message, strlen(message)) <= 0) {
        goto cleanup;
    }
    
    // 分配签名缓冲区
    signature = OPENSSL_malloc(sig_len);
    if (!signature) {
        goto cleanup;
    }
    
    // 生成签名
    if (EVP_DigestSign(md_ctx, signature, &sig_len,
                       (const unsigned char*)message, strlen(message)) <= 0) {
        goto cleanup;
    }
    
    // 验证签名
    EVP_MD_CTX_free(md_ctx);
    md_ctx = EVP_MD_CTX_new();
    if (!md_ctx) {
        goto cleanup;
    }
    
    if (EVP_DigestVerifyInit(md_ctx, &pctx, EVP_sm3(), NULL, pkey) <= 0) {
        goto cleanup;
    }
    
    if (EVP_PKEY_CTX_set1_id(pctx, "1234567812345678", 16) <= 0) {
        goto cleanup;
    }
    
    if (EVP_DigestVerify(md_ctx, signature, sig_len,
                         (const unsigned char*)message, strlen(message)) == 1) {
        printf("SM2 signature verification successful\n");
        ret = 1;
    }
    
cleanup:
    EVP_MD_CTX_free(md_ctx);
    EVP_PKEY_free(pkey);
    EC_KEY_free(key);
    OPENSSL_free(signature);
    return ret;
}
```

### 2.2 SM4分组密码

```c
#include <openssl/evp.h>
#include <openssl/sm4.h>

int sm4_encrypt_decrypt_example(void) {
    EVP_CIPHER_CTX *ctx = NULL;
    const unsigned char key[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    const unsigned char iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };
    const char *plaintext = "Hello, SM4 encryption!";
    unsigned char ciphertext[64];
    unsigned char decrypted[64];
    int len, ciphertext_len, decrypted_len;
    int ret = 0;
    
    // 创建加密上下文
    ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        return 0;
    }
    
    // 初始化加密操作
    if (EVP_EncryptInit_ex(ctx, EVP_sm4_cbc(), NULL, key, iv) != 1) {
        goto cleanup;
    }
    
    // 加密数据
    if (EVP_EncryptUpdate(ctx, ciphertext, &len, 
                          (const unsigned char*)plaintext, strlen(plaintext)) != 1) {
        goto cleanup;
    }
    ciphertext_len = len;
    
    // 完成加密
    if (EVP_EncryptFinal_ex(ctx, ciphertext + len, &len) != 1) {
        goto cleanup;
    }
    ciphertext_len += len;
    
    printf("SM4 encryption successful, ciphertext length: %d\n", ciphertext_len);
    
    // 重新初始化解密操作
    if (EVP_DecryptInit_ex(ctx, EVP_sm4_cbc(), NULL, key, iv) != 1) {
        goto cleanup;
    }
    
    // 解密数据
    if (EVP_DecryptUpdate(ctx, decrypted, &len, ciphertext, ciphertext_len) != 1) {
        goto cleanup;
    }
    decrypted_len = len;
    
    // 完成解密
    if (EVP_DecryptFinal_ex(ctx, decrypted + len, &len) != 1) {
        goto cleanup;
    }
    decrypted_len += len;
    
    // 添加字符串结束符
    decrypted[decrypted_len] = '\0';
    
    if (strcmp((const char*)decrypted, plaintext) == 0) {
        printf("SM4 decryption successful: %s\n", decrypted);
        ret = 1;
    }
    
cleanup:
    EVP_CIPHER_CTX_free(ctx);
    return ret;
}
```

## 3. TLCP协议使用示例

### 3.1 TLCP客户端

```c
#include <openssl/ssl.h>
#include <openssl/err.h>

int tlcp_client_example(void) {
    SSL_CTX *ctx = NULL;
    SSL *ssl = NULL;
    int ret = 0;
    
    // 创建TLCP上下文
    ctx = SSL_CTX_new(NTLS_client_method());
    if (!ctx) {
        printf("Failed to create TLCP context\n");
        return 0;
    }
    
    // 设置国密密码套件
    if (SSL_CTX_set_cipher_list(ctx, "ECC-SM2-SM4-CBC-SM3") != 1) {
        printf("Failed to set cipher list\n");
        goto cleanup;
    }
    
    // 加载证书和私钥
    if (SSL_CTX_use_certificate_file(ctx, "client_sign.crt", SSL_FILETYPE_PEM) != 1) {
        printf("Failed to load client certificate\n");
        goto cleanup;
    }
    
    if (SSL_CTX_use_PrivateKey_file(ctx, "client_sign.key", SSL_FILETYPE_PEM) != 1) {
        printf("Failed to load client private key\n");
        goto cleanup;
    }
    
    // 创建SSL连接
    ssl = SSL_new(ctx);
    if (!ssl) {
        printf("Failed to create SSL object\n");
        goto cleanup;
    }
    
    // 这里应该设置socket连接，在Trusty环境中需要适配
    // SSL_set_fd(ssl, socket_fd);
    
    printf("TLCP client setup successful\n");
    ret = 1;
    
cleanup:
    SSL_free(ssl);
    SSL_CTX_free(ctx);
    return ret;
}
```

## 4. 构建和测试

### 4.1 构建命令

```bash
# 在trusty-tee根目录下
make COMPILER_PATH=/path/to/compiler tongsuo_test

# 或者构建整个项目
make COMPILER_PATH=/path/to/compiler testproject
```

### 4.2 运行测试

```bash
# 在模拟器中运行
./out/build-testproject/lk.bin

# 或者在真实硬件上部署测试
```

## 5. 性能优化建议

### 5.1 编译优化

```makefile
# 在项目配置中启用优化
TONGSUO_ENABLE_ASM := true          # 启用汇编优化
TONGSUO_ENABLE_SMALL := true        # 启用小体积优化
TONGSUO_ENABLE_FAST := false        # 禁用快速但大体积的优化
```

### 5.2 内存优化

```c
// 在应用初始化时设置内存限制
OPENSSL_init_crypto(OPENSSL_INIT_NO_LOAD_CONFIG, NULL);

// 使用栈分配而不是堆分配（适用于小数据）
unsigned char buffer[256];  // 而不是 malloc
```

## 6. 故障排除

### 6.1 常见编译错误

1. **缺少头文件**：确保包含路径正确设置
2. **链接错误**：检查MODULE_LIBRARY_DEPS配置
3. **符号未定义**：可能需要启用相应的算法支持

### 6.2 运行时错误

1. **内存不足**：增加应用的heap和stack大小
2. **算法不支持**：检查编译时是否启用了相应算法
3. **证书加载失败**：在Trusty环境中需要特殊的证书加载方式

这个使用示例展示了如何在Trusty环境中使用集成的Tongsuo库，包括国密算法和TLCP协议的基本用法。
