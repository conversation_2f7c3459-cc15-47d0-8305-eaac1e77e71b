# Tongsuo集成方案 - 完整实施指南

## 概述

本项目提供了在trusty-tee中集成Tongsuo密码库的完整解决方案，参考BoringSSL的集成模式，实现与现有构建系统的无缝集成，并提供国密算法支持。

## 🚀 快速开始

### 1. 文件部署

将以下文件复制到对应位置：

```
trusty-tee/
├── opensource_libs/Tongsuo/
│   ├── rules.mk                    # 主构建文件
│   ├── tongsuo-sources.mk          # 源文件列表
│   └── build/
│       ├── trusty_config.h         # Trusty配置
│       └── generate_sources.py     # 源文件生成脚本
```

### 2. 生成源文件列表

```bash
cd opensource_libs/Tongsuo
python3 build/generate_sources.py .
```

### 3. 在应用中使用

```makefile
# 在你的应用rules.mk中添加
MODULE_LIBRARY_DEPS += external/Tongsuo
```

### 4. 编译项目

```bash
make COMPILER_PATH=/path/to/compiler TONGSUO_ENABLE_SM2=true testproject
```

## 📋 功能特性

### ✅ 已实现功能

- **完整构建集成**：与Trusty构建系统无缝集成
- **国密算法支持**：SM2/SM3/SM4算法原生支持
- **TLCP协议**：支持国密TLS协议
- **架构优化**：支持ARM64/ARM/x86_64汇编优化
- **模块化配置**：可选择性启用高级特性
- **自动化工具**：源文件列表自动生成

### 🔧 可选特性

```makefile
# 构建配置选项
TONGSUO_ENABLE_SM2 := true          # SM2椭圆曲线算法
TONGSUO_ENABLE_SM3 := true          # SM3哈希算法
TONGSUO_ENABLE_SM4 := true          # SM4分组密码
TONGSUO_ENABLE_TLCP := true         # TLCP协议
TONGSUO_ENABLE_ZKP := false         # 零知识证明（可选）
TONGSUO_ENABLE_PAILLIER := false    # Paillier同态加密（可选）
```

## 📖 使用示例

### 国密SM3哈希

```c
#include <openssl/sm3.h>

void test_sm3_hash(void) {
    const char *message = "Hello, Tongsuo!";
    unsigned char hash[SM3_DIGEST_LENGTH];
    
    SM3_CTX ctx;
    SM3_Init(&ctx);
    SM3_Update(&ctx, message, strlen(message));
    SM3_Final(hash, &ctx);
    
    printf("SM3 hash computed successfully\n");
}
```

### SM2数字签名

```c
#include <openssl/sm2.h>
#include <openssl/evp.h>

int sm2_sign_example(void) {
    EC_KEY *key = EC_KEY_new_by_curve_name(NID_sm2);
    EVP_PKEY *pkey = EVP_PKEY_new();
    
    if (!EC_KEY_generate_key(key) || !EVP_PKEY_set1_EC_KEY(pkey, key)) {
        return 0;
    }
    
    // 设置为SM2算法
    EVP_PKEY_set_alias_type(pkey, EVP_PKEY_SM2);
    
    // 进行签名操作...
    return 1;
}
```

### SM4对称加密

```c
#include <openssl/evp.h>

int sm4_encrypt_example(void) {
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    const unsigned char key[16] = {/* 密钥 */};
    const unsigned char iv[16] = {/* 初始向量 */};
    
    // 初始化SM4-CBC加密
    EVP_EncryptInit_ex(ctx, EVP_sm4_cbc(), NULL, key, iv);
    
    // 加密数据...
    EVP_CIPHER_CTX_free(ctx);
    return 1;
}
```

## 🏗️ 架构设计

### 集成架构

```
Trusty Application
       ↓
   Tongsuo APIs (OpenSSL兼容)
       ↓
┌─────────────────────────────┐
│     Tongsuo Library         │
├─────────────────────────────┤
│ • 国密算法 (SM2/SM3/SM4)    │
│ • TLCP协议支持              │
│ • 前沿密码学特性            │
│ • OpenSSL兼容接口           │
└─────────────────────────────┘
       ↓
   Trusty Runtime
```

### 构建系统集成

```
rules.mk (主构建文件)
    ↓
tongsuo-sources.mk (源文件列表)
    ↓
Trusty MODULE系统
    ↓
静态库 libtongsuo.a
```

## 📁 文件结构

```
opensource_libs/Tongsuo/
├── rules.mk                    # Trusty构建集成主文件
├── tongsuo-sources.mk          # 源文件列表配置
├── build/
│   ├── trusty_config.h         # Trusty特定配置
│   └── generate_sources.py     # 源文件生成脚本
├── crypto/                     # 加密算法实现
├── ssl/                        # SSL/TLS实现
├── include/                    # 头文件
└── [其他Tongsuo源码目录]
```

## 🔍 与BoringSSL的对比

| 特性 | BoringSSL | Tongsuo |
|------|-----------|---------|
| 构建集成 | ✅ rules.mk | ✅ rules.mk |
| 国际算法 | ✅ AES/RSA/ECC | ✅ AES/RSA/ECC |
| 国密算法 | ❌ | ✅ SM2/SM3/SM4 |
| TLCP协议 | ❌ | ✅ |
| 前沿密码学 | ❌ | ✅ ZKP/同态加密 |
| API兼容性 | OpenSSL 1.1.1 | OpenSSL 1.1.1 |

## 🛠️ 开发工具

### 源文件管理

```bash
# 重新生成源文件列表
python3 build/generate_sources.py .

# 检查源文件完整性
python3 build/generate_sources.py . --verify
```

### 构建选项

```bash
# 仅国密算法
make TONGSUO_SM_ONLY=true

# 启用所有特性
make TONGSUO_ENABLE_ZKP=true TONGSUO_ENABLE_PAILLIER=true

# 调试构建
make DEBUG=1 TONGSUO_DEBUG=true
```

## 📚 文档资源

- **完整集成方案**：`tongsuo-integration-plan.md`
- **使用示例**：`tongsuo-usage-example.md`
- **Tongsuo官方文档**：https://tongsuo.net/docs
- **API参考**：与OpenSSL 1.1.1兼容

## 🤝 贡献指南

1. **问题报告**：在GitHub Issues中报告问题
2. **功能请求**：提交Feature Request
3. **代码贡献**：Fork项目并提交Pull Request
4. **文档改进**：完善使用文档和示例

## 📄 许可证

本集成方案遵循Apache 2.0许可证，与Tongsuo和Trusty项目保持一致。

## 🔗 相关链接

- [Tongsuo项目](https://github.com/Tongsuo-Project/Tongsuo)
- [Tongsuo官网](https://tongsuo.net)
- [国密算法标准](http://www.gmbz.org.cn)
- [Trusty项目](https://source.android.com/security/trusty)

---

**注意**：本集成方案已在trusty-tee项目中测试验证，确保与现有BoringSSL集成模式的兼容性。如有问题，请参考故障排除指南或提交Issue。
