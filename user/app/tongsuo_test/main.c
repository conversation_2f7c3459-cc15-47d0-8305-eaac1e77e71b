#include <stdio.h>
#include <string.h>
#include <openssl/opensslv.h>
#include <openssl/crypto.h>
#include <openssl/ssl.h>

// 测试国密算法
#ifdef OPENSSL_ENABLE_SM3
#include <openssl/sm3.h>
#endif

#ifdef OPENSSL_ENABLE_SM4
#include <openssl/sm4.h>
#endif

#ifdef OPENSSL_ENABLE_SM2
#include <openssl/sm2.h>
#include <openssl/ec.h>
#endif

int main(void) {
    printf("=== Tongsuo Integration Test ===\n");
    
    // 显示版本信息
    printf("OpenSSL version: %s\n", OPENSSL_VERSION_TEXT);
    printf("Tongsuo integration successful!\n");
    
    // 初始化SSL库
    SSL_library_init();
    SSL_load_error_strings();
    printf("SSL library initialized\n");
    
#ifdef OPENSSL_ENABLE_SM3
    // 测试SM3哈希算法
    printf("\n--- Testing SM3 Hash ---\n");
    const char *message = "Hello, Tongsuo!";
    unsigned char hash[SM3_DIGEST_LENGTH];
    
    SM3_CTX ctx;
    if (SM3_Init(&ctx) && 
        SM3_Update(&ctx, message, strlen(message)) && 
        SM3_Final(hash, &ctx)) {
        printf("SM3 hash test: PASSED\n");
        printf("Message: %s\n", message);
        printf("SM3 hash: ");
        for (int i = 0; i < SM3_DIGEST_LENGTH; i++) {
            printf("%02x", hash[i]);
        }
        printf("\n");
    } else {
        printf("SM3 hash test: FAILED\n");
    }
#else
    printf("SM3 algorithm not enabled\n");
#endif

#ifdef OPENSSL_ENABLE_SM4
    // 测试SM4对称加密
    printf("\n--- Testing SM4 Cipher ---\n");
    const unsigned char key[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    const unsigned char plaintext[16] = "Hello SM4!";
    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    
    SM4_KEY sm4_key;
    SM4_set_key(key, &sm4_key);
    
    SM4_encrypt(plaintext, ciphertext, &sm4_key);
    SM4_decrypt(ciphertext, decrypted, &sm4_key);
    
    if (memcmp(plaintext, decrypted, 16) == 0) {
        printf("SM4 cipher test: PASSED\n");
        printf("Plaintext:  %s\n", (char*)plaintext);
        printf("Decrypted:  %s\n", (char*)decrypted);
    } else {
        printf("SM4 cipher test: FAILED\n");
    }
#else
    printf("SM4 algorithm not enabled\n");
#endif

#ifdef OPENSSL_ENABLE_SM2
    // 测试SM2椭圆曲线算法
    printf("\n--- Testing SM2 Key Generation ---\n");
    EC_KEY *key = EC_KEY_new_by_curve_name(NID_sm2);
    if (key) {
        if (EC_KEY_generate_key(key) == 1) {
            printf("SM2 key generation test: PASSED\n");
            printf("SM2 key pair generated successfully\n");
        } else {
            printf("SM2 key generation test: FAILED\n");
        }
        EC_KEY_free(key);
    } else {
        printf("SM2 key creation test: FAILED\n");
    }
#else
    printf("SM2 algorithm not enabled\n");
#endif

    // 测试基本的OpenSSL功能
    printf("\n--- Testing Basic OpenSSL Functions ---\n");
    
    // 测试随机数生成
    unsigned char random_bytes[16];
    if (RAND_bytes(random_bytes, sizeof(random_bytes)) == 1) {
        printf("Random number generation: PASSED\n");
        printf("Random bytes: ");
        for (int i = 0; i < 16; i++) {
            printf("%02x", random_bytes[i]);
        }
        printf("\n");
    } else {
        printf("Random number generation: FAILED\n");
    }
    
    // 测试MD5哈希（作为基本功能测试）
    printf("\n--- Testing MD5 Hash (Basic Function) ---\n");
    unsigned char md5_hash[16];
    if (MD5((unsigned char*)message, strlen(message), md5_hash) != NULL) {
        printf("MD5 hash test: PASSED\n");
        printf("MD5 hash: ");
        for (int i = 0; i < 16; i++) {
            printf("%02x", md5_hash[i]);
        }
        printf("\n");
    } else {
        printf("MD5 hash test: FAILED\n");
    }
    
    printf("\n=== All Tests Completed ===\n");
    printf("Tongsuo integration working correctly!\n");
    
    return 0;
}
