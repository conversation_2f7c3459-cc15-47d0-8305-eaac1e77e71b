# Tongsuo集成方案 - 像BoringSSL一样集成Tongsuo

## 概述

本文档提供了在trusty-tee项目中集成Tongsuo密码库的完整方案，参考BoringSSL的集成模式，实现与现有构建系统的无缝集成。

## 1. 现状分析

### 1.1 BoringSSL集成模式分析

通过分析现有BoringSSL的集成方式，发现以下关键特点：

1. **构建配置文件**：
   - `opensource_libs/boringssl/rules.mk` - Trusty构建系统集成
   - `opensource_libs/boringssl/crypto-sources.mk` - 源文件列表
   - `opensource_libs/boringssl/sources.mk` - 自动生成的源文件配置

2. **构建系统集成**：
   - 使用Trusty的MODULE系统
   - 支持多架构编译（arm, arm64, x86_64）
   - 静态链接库形式提供
   - 导出头文件到全局包含路径

3. **编译配置**：
   - 特定的编译标志：`-D__linux__ -D__TRUSTY__`
   - 禁用C11原子操作：`-D__STDC_NO_ATOMICS__`
   - 架构相关的静态能力检测

### 1.2 Tongsuo现状

1. **已存在于代码库**：`opensource_libs/Tongsuo/`
2. **缺少Trusty集成**：没有rules.mk文件
3. **构建系统**：使用传统的Configure + Makefile系统
4. **特色功能**：
   - 国密算法支持（SM2, SM3, SM4）
   - TLCP协议支持
   - 前沿密码学特性（零知识证明、同态加密等）

## 2. 集成方案设计

### 2.1 整体架构

```
opensource_libs/Tongsuo/
├── rules.mk                    # 新增：Trusty构建集成
├── crypto-sources.mk           # 新增：加密库源文件列表
├── ssl-sources.mk              # 新增：SSL库源文件列表
├── tongsuo-sources.mk          # 新增：统一源文件配置
└── build/                      # 新增：构建辅助脚本
    ├── generate_sources.py     # 源文件列表生成脚本
    └── trusty_config.h         # Trusty特定配置头文件
```

### 2.2 核心集成文件

#### 2.2.1 rules.mk 主构建文件

参考BoringSSL的rules.mk结构，创建Tongsuo的构建配置：

```makefile
# Tongsuo rules.mk for Trusty integration
LOCAL_DIR := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

# 包含源文件配置
include $(LOCAL_DIR)/tongsuo-sources.mk

# Trusty特定编译标志
MODULE_CFLAGS += \
    -D__linux__ \
    -D__TRUSTY__ \
    -DOPENSSL_NO_STDIO \
    -DOPENSSL_NO_SOCK \
    -DOPENSSL_NO_DGRAM \
    -DOPENSSL_NO_UI_CONSOLE \
    -DOPENSSL_NO_DEPRECATED \
    -DOPENSSL_API_COMPAT=0x10101000L

# 国密算法启用
MODULE_CFLAGS += \
    -DOPENSSL_ENABLE_SM2 \
    -DOPENSSL_ENABLE_SM3 \
    -DOPENSSL_ENABLE_SM4

# 架构相关配置
ifeq ($(ARCH),arm64)
MODULE_CFLAGS += -DOPENSSL_AARCH64
endif

# 源文件配置
MODULE_SRCS += $(TONGSUO_CRYPTO_SRCS)
MODULE_SRCS += $(TONGSUO_SSL_SRCS)

# 头文件导出
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

# 内部头文件
MODULE_INCLUDES += \
    $(LOCAL_DIR)/crypto \
    $(LOCAL_DIR)/ssl \
    $(LOCAL_DIR)/include/internal

include make/rctee_lib.mk
```

### 2.3 源文件管理策略

#### 2.3.1 自动化源文件列表生成

创建Python脚本自动从Tongsuo的build.info文件生成Makefile兼容的源文件列表：

```python
#!/usr/bin/env python3
# generate_sources.py - 从build.info生成源文件列表

import os
import re
from pathlib import Path

def parse_build_info(build_info_path):
    """解析build.info文件，提取源文件列表"""
    # 实现build.info解析逻辑
    pass

def generate_makefile_sources(crypto_srcs, ssl_srcs, output_path):
    """生成Makefile格式的源文件列表"""
    # 生成tongsuo-sources.mk文件
    pass
```

#### 2.3.2 条件编译支持

支持根据Trusty环境特点进行条件编译：

```makefile
# 根据功能需求选择性编译
ifeq ($(TONGSUO_ENABLE_SM_ONLY),true)
    # 仅编译国密算法相关代码
    MODULE_CFLAGS += -DOPENSSL_SM_ONLY
endif

ifeq ($(TONGSUO_ENABLE_ZKP),true)
    # 启用零知识证明功能
    MODULE_CFLAGS += -DOPENSSL_ENABLE_ZKP
    MODULE_SRCS += $(TONGSUO_ZKP_SRCS)
endif
```

## 3. 实施步骤

### 3.1 第一阶段：基础集成

1. **创建构建配置文件**
   - 编写`rules.mk`主构建文件
   - 创建源文件列表配置
   - 添加Trusty特定编译选项

2. **最小化功能验证**
   - 仅启用核心加密功能
   - 禁用不兼容的特性（stdio, socket等）
   - 验证基本编译通过

3. **测试集成**
   - 创建简单的测试应用
   - 验证基本加密功能
   - 确保与现有系统兼容

### 3.2 第二阶段：功能扩展

1. **国密算法集成**
   - 启用SM2/SM3/SM4算法
   - 添加国密相关测试
   - 验证算法正确性

2. **SSL/TLS功能**
   - 集成SSL库
   - 支持TLCP协议
   - 添加TLS测试用例

3. **性能优化**
   - 启用汇编优化
   - 架构相关优化
   - 内存使用优化

### 3.3 第三阶段：高级特性

1. **前沿密码学**
   - 零知识证明
   - 同态加密
   - 门限密码学

2. **完整性验证**
   - 全面测试套件
   - 性能基准测试
   - 安全性验证

## 4. 配置选项

### 4.1 编译时配置

```makefile
# 在项目配置中可选择的Tongsuo特性
TONGSUO_ENABLE_SM2 := true          # 启用SM2椭圆曲线算法
TONGSUO_ENABLE_SM3 := true          # 启用SM3哈希算法  
TONGSUO_ENABLE_SM4 := true          # 启用SM4分组密码
TONGSUO_ENABLE_TLCP := true         # 启用TLCP协议
TONGSUO_ENABLE_ZKP := false         # 启用零知识证明（可选）
TONGSUO_ENABLE_PAILLIER := false    # 启用Paillier同态加密（可选）
```

### 4.2 运行时配置

通过环境变量或配置文件控制运行时行为：

```c
// 示例：运行时算法选择
#ifdef OPENSSL_ENABLE_SM2
    // 优先使用国密算法
    EVP_set_default_properties(NULL, "provider=default,sm2=yes");
#endif
```

## 5. 与BoringSSL的兼容性

### 5.1 API兼容性

- 保持OpenSSL 1.1.1 API兼容性
- 提供BoringSSL兼容层（如需要）
- 渐进式迁移策略

### 5.2 构建系统兼容性

- 使用相同的MODULE系统
- 保持相同的目录结构
- 兼容现有的依赖关系

## 6. 测试策略

### 6.1 单元测试

- 移植Tongsuo自带测试套件
- 添加Trusty特定测试
- 国密算法专项测试

### 6.2 集成测试

- 与现有应用的兼容性测试
- 性能回归测试
- 安全性测试

## 7. 文档和维护

### 7.1 文档更新

- 更新构建文档
- 添加Tongsuo使用指南
- 国密算法使用示例

### 7.2 维护策略

- 定期同步上游Tongsuo更新
- 维护Trusty特定补丁
- 版本管理策略

## 8. 风险评估和缓解

### 8.1 主要风险

1. **兼容性风险**：Tongsuo与Trusty环境的兼容性问题
2. **性能风险**：可能的性能回归
3. **安全风险**：新引入的安全漏洞
4. **维护风险**：长期维护成本

### 8.2 缓解措施

1. **渐进式集成**：分阶段实施，降低风险
2. **充分测试**：全面的测试覆盖
3. **回滚机制**：保持BoringSSL作为备选方案
4. **社区支持**：积极参与Tongsuo社区

## 9. 实施文件清单

本方案已创建以下实施文件：

### 9.1 核心集成文件
- `opensource_libs/Tongsuo/rules.mk` - Trusty构建系统集成主文件
- `opensource_libs/Tongsuo/tongsuo-sources.mk` - 源文件列表配置
- `opensource_libs/Tongsuo/build/trusty_config.h` - Trusty特定配置头文件
- `opensource_libs/Tongsuo/build/generate_sources.py` - 源文件列表生成脚本

### 9.2 文档和示例
- `tongsuo-integration-plan.md` - 完整集成方案文档
- `tongsuo-usage-example.md` - 使用示例和测试代码

### 9.3 使用方法

1. **生成源文件列表**：
   ```bash
   cd opensource_libs/Tongsuo
   python3 build/generate_sources.py .
   ```

2. **在应用中使用**：
   ```makefile
   MODULE_LIBRARY_DEPS += external/Tongsuo
   ```

3. **编译项目**：
   ```bash
   make COMPILER_PATH=/path/to/compiler TONGSUO_ENABLE_SM2=true testproject
   ```

## 10. 总结

本方案提供了完整的Tongsuo集成路径，通过参考BoringSSL的成功经验，确保集成过程的稳定性和可靠性。重点关注国密算法支持和前沿密码学特性，为trusty-tee项目提供更强大的密码学能力。

### 10.1 主要优势

1. **完全兼容**：与现有BoringSSL集成模式保持一致
2. **国密支持**：原生支持SM2/SM3/SM4算法和TLCP协议
3. **模块化设计**：可选择性启用高级密码学特性
4. **性能优化**：支持架构相关的汇编优化
5. **易于维护**：自动化源文件管理和构建配置

### 10.2 后续工作

1. **测试验证**：全面测试各项功能的正确性和性能
2. **文档完善**：补充API文档和最佳实践指南
3. **社区贡献**：向Tongsuo社区贡献Trusty适配代码
4. **持续集成**：建立自动化测试和构建流程
